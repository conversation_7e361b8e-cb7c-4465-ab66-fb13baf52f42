import type { GetForEditInput, PagedSortedAndFilteredInputDto } from '../../../helper/shared/models'
import type { PurchaseItemsType } from '../purchase-items-type.enum'
import type { AuditedEntityDto, EntityDto, FullAuditedEntityDto } from '@/api/app/model/baseModel'

export interface GetPurchaseItemsForEditorOutput {
    purchaseItems: PurchaseItemsEditDto
}

export interface GetPurchaseItemsInput extends PagedSortedAndFilteredInputDto {
    purchaseOrderId?: string
    status?: any
    isDeleted?: boolean
    type?: PurchaseItemsType
    sorting?: string
}

export interface GetPurchaseOrderForEditorOutput {
    purchaseOrder: PurchaseOrderEditDto
}

export interface GetPurchaseOrderInput extends PagedSortedAndFilteredInputDto {
    status?: any
    applicantUserId?: string
    reviewerUserId?: string
    executorUserId?: string
    isDeleted?: boolean
    sorting?: string
    skipCount?: number
    maxResultCount?: number
}

export interface GetWmCategoryEditorInput extends GetForEditInput {}

export interface GetWmCategoryForEditorOutput {
    wmCategory: WmCategoryEditDto
}

export interface GetWmCategoryInput extends PagedSortedAndFilteredInputDto {
    parentId?: string
    isActivate?: boolean
    isDeleted?: boolean
    sorting?: string
    skipCount?: number
    maxResultCount?: number
}

export interface GetWmInWarehouseBillForEditorOutput {
    wmInWarehouseBill: WmInWarehouseBillEditDto
}

export interface GetWmInWarehouseBillInput extends PagedSortedAndFilteredInputDto {
    storageType?: any
    auditStatus?: any
    applicantUserId?: string
    reviewerUserId?: string
    executorUserId?: string
    isDeleted?: boolean
    sorting?: string
}

export interface GetWmInWarehouseListForEditorOutput {
    wmInWarehouseList: WmInWarehouseListEditDto
}

export interface GetWmInWarehouseListInput extends PagedSortedAndFilteredInputDto {
    status?: any
    inWarehouseBillId?: string
    materialId?: string
    isDeleted?: boolean
    sorting?: string
}

export interface GetWmMaterialForEditorOutput {
    wmMaterial: WmMaterialEditDto
    materialsTypeEnum: any
    materialsAuditStatusEnum: any
}

export interface GetWmMaterialInput extends PagedSortedAndFilteredInputDto {
    categoryId?: string
    supplierId?: string
    isActivate?: boolean
    materialsType?: any
    materialsAuditStatus?: any
    isDeleted?: boolean
    sorting?: string
    name?: string
}

export interface GetWmMeasureUnitForEditorOutput {
    wmMeasureUnit: WmMeasureUnitEditDto
}

export interface GetWmMeasureUnitInput extends PagedSortedAndFilteredInputDto {
    primaryUnitId?: string
    isPrimaryUnit?: boolean
    isActivate?: boolean
    isDeleted?: boolean
    sorting?: string
}

export interface GetWmOutWarehouseBillForEditorOutput {
    wmOutWarehouseBill: WmOutWarehouseBillEditDto
}

export interface GetWmOutWarehouseBillInput extends PagedSortedAndFilteredInputDto {
    outboundType?: any
    auditStatus?: any
    applicantUserId?: string
    reviewerUserId?: string
    executorUserId?: string
    isDeleted?: boolean
    sorting?: string
}

export interface GetWmOutWarehouseListForEditorOutput {
    wmOutWarehouseList: WmOutWarehouseListEditDto
}

export interface GetWmOutWarehouseListInput extends PagedSortedAndFilteredInputDto {
    status?: any
    outWarehouseBillId?: string
    materialId?: string
    isDeleted?: boolean
    sorting?: string
}

export interface GetWmProductBomEditorInput extends GetForEditInput {}

export interface GetWmProductBomForEditorOutput {
    wmProductBom: WmProductBomEditDto
}

export interface GetWmProductBomInput extends PagedSortedAndFilteredInputDto {
    type?: any
    isActivate?: boolean
    bomId?: string
    materialId?: string
    isDeleted?: boolean
    sorting?: string
}

export interface GetWmStockQuantityForEditorOutput {
    wmStockQuantity: WmStockQuantityEditDto
}

export interface GetWmStockQuantityInput extends PagedSortedAndFilteredInputDto {
    materialId?: string
    categoryId?: string
    isDeleted?: boolean
    sorting?: string
}

export interface GetWmSupplierForEditorOutput {
    wmSupplier: WmSupplierEditDto
    wmSupplierLevelEnum: any
}

export interface GetWmSupplierInput extends PagedSortedAndFilteredInputDto {
    isActivate?: boolean
    isDeleted?: boolean
    sorting?: string
}

export interface GetWmWarehouseAreaForEditorOutput {
    wmWarehouseArea: WmWarehouseAreaEditDto
}

export interface GetWmWarehouseAreaInput extends PagedSortedAndFilteredInputDto {
    warehouseId?: string
    isDeleted?: boolean
    sorting?: string
}

export interface GetWmWarehouseForEditorOutput {
    wmWarehouse: WmWarehouseEditDto
}

export interface GetWmWarehouseInput extends PagedSortedAndFilteredInputDto {
    productParentId?: string
    isDeleted?: boolean
    sorting?: string
}

export interface GetWmWarehouseLocationForEditorOutput {
    wmWarehouseLocation: WmWarehouseLocationEditDto
}

export interface GetWmWarehouseLocationInput extends PagedSortedAndFilteredInputDto {
    areaId?: string
    isDeleted?: boolean
    sorting?: string
    skipCount?: number
    maxResultCount?: number
}

export interface PurchaseItemsCloneDto {
    id?: string
    status: any
}

export interface PurchaseItemsCreateDto extends PurchaseItemsCreateOrUpdateDtoBase {}

export interface PurchaseItemsCreateOrUpdateDtoBase {
    id?: string
    name?: string
    encode?: string
    description?: string
    orderDate?: string
    eta?: string
    ata?: string
    status: any
    purchaseOrderId: string
    type: any
    materialId?: string
    measureUnitId?: string
    categoryId?: string
    supplierId?: string
    quantity: number
    unitPrice: number
    totalPrice: number
}

export interface PurchaseItemsDetailDto extends EntityDto<string> {
    name?: string
    encode?: string
    description?: string
    orderDate?: string
    eta?: string
    ata?: string
    status: any
    purchaseOrderId: string
    type: any
    materialId?: string
    measureUnitId?: string
    categoryId?: string
    supplierId?: string
    quantity: number
    unitPrice: number
    totalPrice: number
    creatorId?: string
    creationTime?: string
}

export interface PurchaseItemsEditDto extends PurchaseItemsCreateOrUpdateDtoBase {}

export interface PurchaseItemsListDto extends EntityDto<string> {
    name?: string
    encode?: string
    description?: string
    orderDate?: string
    eta?: string
    ata?: string
    status: any
    purchaseOrderId: string
    type: any
    materialId?: string
    measureUnitId?: string
    categoryId?: string
    supplierId?: string
    quantity: number
    unitPrice: number
    totalPrice: number
    creatorId?: string
    creationTime?: string
    model?: string
    specification?: string
    reviewerUserId?: string
    executorUserId?: string
    executorUserName?: string
}

export interface PurchaseItemsUpdateStatusDto {
    id?: string
    status: any
}

export interface PurchaseOrderCloneDto {
    id?: string
    status: any
}

export interface PurchaseOrderCreateDto extends PurchaseOrderCreateOrUpdateDtoBase {}

export interface PurchaseOrderCreateOrUpdateDtoBase {
    id?: string
    name?: string
    encode?: string
    description?: string
    orderDate?: string
    eta?: string
    ata?: string
    status: any
    applicantUserId: string
    applicantUserName?: string
    reviewerUserId: string
    executorUserId: string
    executorUserName?: string
    totalPrice: number
}

export interface PurchaseOrderDetailDto extends EntityDto<string> {
    name?: string
    encode?: string
    description?: string
    orderDate?: string
    eta?: string
    ata?: string
    status: any
    applicantUserId: string
    applicantUserName?: string
    reviewerUserId: string
    executorUserId: string
    executorUserName?: string
    totalPrice: number
    creatorId?: string
    creationTime?: string
}

export interface PurchaseOrderEditDto extends PurchaseOrderCreateOrUpdateDtoBase {}

export interface PurchaseOrderListDto extends EntityDto<string> {
    name?: string
    encode?: string
    description?: string
    orderDate?: string
    eta?: string
    ata?: string
    status: any
    applicantUserId: string
    applicantUserName?: string
    reviewerUserId: string
    executorUserId: string
    executorUserName: string
    totalPrice: number
    creatorId?: string
    creationTime?: string
}

export interface PurchaseOrderUpdateStatusDto {
    id?: string
    status: any
}

export interface WmCategoryCreateDto extends WmCategoryCreateOrUpdateDtoBase {}

export interface WmCategoryCreateOrUpdateDtoBase {
    id?: string
    name: string
    describe?: string
    isActivate: boolean
    sort: number
    categoryId?: string
}

export interface WmCategoryDetailDto extends AuditedEntityDto<string> {
    name: string
    describe?: string
    isActivate: boolean
    sort: number
    categoryId?: string
    categoryName?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
}

export interface WmCategoryEditDto extends WmCategoryCreateOrUpdateDtoBase {}

export interface WmCategoryTreeListDto {
    id?: string
    name?: string
    serialNo: number
    encode?: string
    describe?: string
    images?: string
    isActivate: boolean
    produceDutyUserId?: string
    maintainDutyUserId?: string
    produceDutyUserName?: string
    maintainDutyUserName?: string
    categoryId?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    isDeleted: boolean
    deleterId?: string
    deletionTime?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
    children: WmCategoryTreeListDto[]
}

export interface WmCategoryTreeNodesDto {
    id?: string
    name?: string
    serialNo: number
    encode?: string
    describe?: string
    isActivate: boolean
    categoryId?: string
    children: WmCategoryTreeNodesDto[]
}

export interface WmInWarehouseBillCloneDto {
    id?: string
}

export interface WmInWarehouseBillCreateDto extends WmInWarehouseBillCreateOrUpdateDtoBase {}

export interface WmInWarehouseBillCreateOrUpdateDtoBase {
    id?: string
    name: string
    encode: string
    describe?: string
    storageType: any
    status: any
    storageTime?: string
    applicantUserId: string
    applicantUserName?: string
    reviewerUserId: string
    executorUserId: string
    executorUserName?: string
    batchCode?: string
}

export interface WmInWarehouseBillDetailDto extends EntityDto<string> {
    name: string
    encode: string
    describe?: string
    storageType: any
    status: any
    storageTime?: string
    applicantUserId: string
    applicantUserName?: string
    reviewerUserId: string
    executorUserId: string
    executorUserName?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
}

export interface WmInWarehouseBillEditDto extends WmInWarehouseBillCreateOrUpdateDtoBase {}

export interface WmInWarehouseBillListDto extends EntityDto<string> {
    name: string
    encode: string
    describe?: string
    storageType: any
    status: any
    storageTime?: string
    applicantUserId: string
    applicantUserName?: string
    reviewerUserId: string
    executorUserId: string
    executorUserName?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
}

export interface WmInWarehouseBilllUpdateStatusDto {
    id?: string
    status: any
}

export interface WmInWarehouseListCloneDto {
    id?: string
}

export interface WmInWarehouseListCreateDto extends WmInWarehouseListCreateOrUpdateDtoBase {}

export interface WmInWarehouseListCreateOrUpdateDtoBase {
    id?: string
    name: string
    encode: string
    describe?: string
    quantity: number
    status: any
    materialId?: string
    inWarehouseBillId?: string
    locationId?: string
}

export interface WmInWarehouseListDetailDto extends EntityDto<string> {
    name: string
    encode: string
    describe?: string
    quantity: number
    status: any
    materialId?: string
    inWarehouseBillId?: string
    locationId?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
}

export interface WmInWarehouseListEditDto extends WmInWarehouseListCreateOrUpdateDtoBase {}

export interface WmInWarehouseListListDto extends EntityDto<string> {
    name: string
    encode: string
    describe?: string
    quantity: number
    status: any
    materialId?: string
    inWarehouseBillId?: string
    locationId?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
}

export interface WmInWarehouseListProcessPartialDto {
    id?: string
    status: any
    quantity: number
}

export interface WmInWarehouseListUpdateStatusDto {
    id?: string
    status: any
}

export interface WmInWarehouseListSubmitDto {
    id: string
}

export interface WmInWarehouseListInProgressDto {
    id: string
}

export interface WmInWarehouseListIsInDto {
    id: string
    isIn: boolean
}

export interface WmInWarehouseListIsCancelDto {
    id: string
    isCancel: boolean
}

export interface WmMaterialCreateDto extends WmMaterialCreateOrUpdateDtoBase {}

export interface WmMaterialCreateOrUpdateDtoBase {
    id?: string
    name: string
    encode: string
    model: string
    specification?: string
    describe?: string
    type: any
    isActivate: boolean
    safeStock?: string
    minStock: number
    maxStock: number
    materialsAuditStatus: any
    images?: string
    measureUnitId?: string
    categoryId?: string
    supplierIds?: string[]
}

export interface WmMaterialDetailDto extends FullAuditedEntityDto<string> {
    name: string
    encode: string
    model: string
    specification?: string
    describe?: string
    type: any
    isActivate: boolean
    safeStock?: string
    minStock: number
    maxStock: number
    status?: string
    images?: string
    measureUnitId?: string
    categoryId?: string
    suppliers?: WmSupplierListDto[]
    unitName?: string
    categoryName?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
}

export interface WmMaterialEditDto extends WmMaterialCreateOrUpdateDtoBase {}

export interface WmMaterialListDto extends FullAuditedEntityDto<string> {
    name: string
    encode: string
    model: string
    specification?: string
    describe?: string
    type: any
    isActivate: boolean
    safeStock?: string
    minStock: number
    maxStock: number
    materialsAuditStatus: any
    images?: string
    measureUnitId?: string
    categoryId?: string
    suppliers?: WmSupplierListDto[]
    measureUnitName?: string
    categoryName?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
}

export interface WmMeasureUnitCreateDto extends WmMeasureUnitCreateOrUpdateDtoBase {}

export interface WmMeasureUnitCreateOrUpdateDtoBase {
    id?: string
    name: string
    encode: string
    describe?: string
    changeRate: number
    isActivate: boolean
    isPrimaryUnit: boolean
    primaryUnitId?: string
}

export interface WmMeasureUnitDetailDto extends EntityDto<string> {
    name: string
    encode: string
    describe?: string
    changeRate: number
    isActivate: boolean
    isPrimaryUnit: boolean
    primaryUnitId?: string
    primaryUnitName?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
}

export interface WmMeasureUnitEditDto extends WmMeasureUnitCreateOrUpdateDtoBase {}

export interface WmMeasureUnitListDto extends EntityDto<string> {
    name: string
    encode: string
    describe?: string
    changeRate: number
    isActivate: boolean
    isPrimaryUnit: boolean
    primaryUnitId?: string
    primaryUnitName?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    isDeleted: boolean
    deleterId?: string
    deletionTime?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
}

export interface WmOutWarehouseBillCloneDto {
    id?: string
}

export interface WmOutWarehouseBillCreateDto extends WmOutWarehouseBillCreateOrUpdateDtoBase {}

export interface WmOutWarehouseBillCreateOrUpdateDtoBase {
    id?: string
    name: string
    encode: string
    describe?: string
    outboundType: any
    outboundTime?: string
    auditStatus: any
    imageUrl?: string
    attachement?: string
    applicantUserId: string
    applicantUserName?: string
    reviewerUserId: string
    executorUserId: string
    executorUserName?: string
    batchCode?: string
}

export interface WmOutWarehouseBillDetailDto extends EntityDto<string> {
    name: string
    encode: string
    describe?: string
    outboundType: any
    outboundTime?: string
    auditStatus: any
    imageUrl?: string
    attachement?: string
    applicantUserId: string
    applicantUserName?: string
    reviewerUserId: string
    executorUserId: string
    executorUserName?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
}

export interface WmOutWarehouseBillEditDto extends WmOutWarehouseBillCreateOrUpdateDtoBase {}

export interface WmOutWarehouseBillListDto extends EntityDto<string> {
    name: string
    encode: string
    describe?: string
    outboundType: any
    outboundTime?: string
    auditStatus: any
    imageUrl?: string
    attachement?: string
    applicantUserId: string
    applicantUserName?: string
    reviewerUserId: string
    executorUserId: string
    executorUserName?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
}

export interface WmOutWarehouseBillUpdateStatusDto {
    id?: string
    status: any
}

export interface WmOutWarehouseListCloneDto {
    id?: string
}

export interface WmOutWarehouseListCreateDto extends WmOutWarehouseListCreateOrUpdateDtoBase {}

export interface WmOutWarehouseListCreateOrUpdateDtoBase {
    id?: string
    name: string
    encode: string
    describe?: string
    quantity: number
    status: any
    materialId?: string
    outWarehouseBillId?: string
    locationId?: string
}

export interface WmOutWarehouseListDetailDto extends EntityDto<string> {
    name: string
    encode: string
    describe?: string
    quantity: number
    status: any
    materialId?: string
    outWarehouseBillId?: string
    locationId?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
}

export interface WmOutWarehouseListEditDto extends WmOutWarehouseListCreateOrUpdateDtoBase {}

export interface WmOutWarehouseListListDto extends EntityDto<string> {
    name: string
    encode: string
    describe?: string
    quantity: number
    status: any
    materialId?: string
    outWarehouseBillId?: string
    locationId?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
}

export interface WmOutWarehouseListProcessPartialDto {
    id?: string
    status: any
    quantity: number
}

export interface WmOutWarehouseListUpdateStatusDto {
    id?: string
    status: any
}

export interface WmProductBomCreateDto extends WmProductBomCreateOrUpdateDtoBase {}

export interface WmProductBomCreateOrUpdateDtoBase {
    id?: string
    name: string
    encode: string
    specification?: string
    describe?: string
    type: any
    quantity: number
    isActivate: boolean
    bomId?: string
    materialId?: string
}

export interface WmProductBomDetailDto extends AuditedEntityDto<string> {
    name: string
    encode: string
    specification?: string
    describe?: string
    type: any
    quantity: number
    isActivate: boolean
    bomId?: string
    materialId?: string
}

export interface WmProductBomEditDto extends WmProductBomCreateOrUpdateDtoBase {}

export interface WmProductBomTreeListDto {
    id?: string
    name: string
    encode: string
    specification?: string
    describe?: string
    type: any
    quantity: number
    isActivate: boolean
    bomId?: string
    materialId?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
    children: WmProductBomTreeListDto[]
}

export interface WmProductBomTreeNodesDto {
    id?: string
    name?: string
    encode?: string
    describe?: string
    isActivate: boolean
    bomId?: string
    children: WmProductBomTreeNodesDto[]
}

export interface WmStockQuantityCreateDto extends WmStockQuantityCreateOrUpdateDtoBase {}

export interface WmStockQuantityCreateOrUpdateDtoBase {
    id?: string
    quantity: number
    costPrice: number
    stockStatus: any
    batchCode?: string
    expireDate?: string
    isFrozen: boolean
    locationId?: string
    materialId?: string
}

export interface WmStockQuantityDetailDto extends WmMaterialListDto {
    quantity: number
    costPrice: number
    stockStatus: any
    batchCode?: string
    expireDate?: string
    isFrozen: boolean
    locationId?: string
    materialId?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
}

export interface WmStockQuantityEditDto extends WmStockQuantityCreateOrUpdateDtoBase {}

export interface WmStockQuantityListDto extends WmMaterialListDto {
    quantity: number
    costPrice: number
    stockStatus: any
    batchCode?: string
    expireDate?: string
    isFrozen: boolean
    locationId?: string
    materialId?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
}

export interface WmSupplierCreateDto extends WmSupplierCreateOrUpdateDtoBase {}

export interface WmSupplierCreateOrUpdateDtoBase {
    id?: string
    name: string
    encode: string
    nickname: string
    enName?: string
    describe?: string
    logo?: string
    level: any
    score: number
    address?: string
    website?: string
    creditCode?: string
    contact?: string
    email?: string
    telephone?: string
    contact1?: string
    contact1Tel?: string
    contact1Email?: string
    contact2?: string
    contact2Tel?: string
    contact2Email?: string
    isActivate: boolean
}

export interface WmSupplierDetailDto extends FullAuditedEntityDto<string> {
    name: string
    encode: string
    nickname: string
    enName?: string
    describe?: string
    logo?: string
    level: any
    score: number
    address?: string
    website?: string
    creditCode?: string
    contact?: string
    email?: string
    telephone?: string
    contact1?: string
    contact1Tel?: string
    contact1Email?: string
    contact2?: string
    contact2Tel?: string
    contact2Email?: string
    isActivate: boolean
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
}

export interface WmSupplierEditDto extends WmSupplierCreateOrUpdateDtoBase {}

export interface WmSupplierListDto extends FullAuditedEntityDto<string> {
    name: string
    encode: string
    nickname: string
    enName?: string
    describe?: string
    logo?: string
    level: any
    score: number
    address?: string
    website?: string
    creditCode?: string
    contact?: string
    email?: string
    telephone?: string
    contact1?: string
    contact1Tel?: string
    contact1Email?: string
    contact2?: string
    contact2Tel?: string
    contact2Email?: string
    isActivate: boolean
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
}

export interface WmWarehouseAreaCreateDto extends WmWarehouseAreaCreateOrUpdateDtoBase {}

export interface WmWarehouseAreaCreateOrUpdateDtoBase {
    id?: string
    name: string
    encode: string
    describe?: string
    location: string
    areaSize: number
    maxLoad?: number
    positionX?: number
    positionY?: number
    positionZ?: number
    isActivate: boolean
    dutyUserId?: string
    warehouseId?: string
}

export interface WmWarehouseAreaDetailDto extends FullAuditedEntityDto<string> {
    name: string
    encode: string
    describe?: string
    location?: string
    areaSize: number
    maxLoad: number
    positionX: number
    positionY: number
    positionZ: number
    isActivate: boolean
    dutyUserId?: string
    areaId?: string
    areaName?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
}

export interface WmWarehouseAreaEditDto extends WmWarehouseAreaCreateOrUpdateDtoBase {}

export interface WmWarehouseAreaListDto extends FullAuditedEntityDto<string> {
    name: string
    encode: string
    describe?: string
    location?: string
    areaSize: number
    maxLoad: number
    positionX: number
    positionY: number
    positionZ: number
    isActivate: boolean
    dutyUserId?: string
    warehouseId?: string
    areaId?: string
    areaName?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
}

export interface WmWarehouseCreateDto extends WmWarehouseCreateOrUpdateDtoBase {}

export interface WmWarehouseCreateOrUpdateDtoBase {
    id?: string
    name: string
    encode: string
    describe?: string
    location?: string
    areaSize: number
    isActivate: boolean
    dutyUserId?: string
}

export interface WmWarehouseDetailDto extends FullAuditedEntityDto<string> {
    name: string
    encode: string
    describe?: string
    location?: string
    areaSize: number
    isActivate: boolean
    dutyUserId?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
}

export interface WmWarehouseEditDto extends WmWarehouseCreateOrUpdateDtoBase {}

export interface WmWarehouseListDto extends FullAuditedEntityDto<string> {
    name: string
    encode: string
    describe?: string
    location?: string
    areaSize: number
    isActivate: boolean
    dutyUserId?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
}

export interface WmWarehouseLocationCreateDto extends WmWarehouseLocationCreateOrUpdateDtoBase {}

export interface WmWarehouseLocationCreateOrUpdateDtoBase {
    id?: string
    name: string
    encode: string
    describe?: string
    location?: string
    areaSize: number
    maxLoad: number
    positionX: number
    positionY: number
    positionZ: number
    isActivate: boolean
    dutyUserId?: string
    areaId?: string
}

export interface WmWarehouseLocationDetailDto extends FullAuditedEntityDto<string> {
    name: string
    encode: string
    describe?: string
    location?: string
    areaSize: number
    maxLoad: number
    positionX: number
    positionY: number
    positionZ: number
    isActivate: boolean
    dutyUserId?: string
    areaId?: string
    areaName?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
}

export interface WmWarehouseLocationEditDto extends WmWarehouseLocationCreateOrUpdateDtoBase {}

export interface WmWarehouseLocationListDto extends FullAuditedEntityDto<string> {
    name: string
    encode: string
    describe?: string
    location?: string
    areaSize: number
    maxLoad: number
    positionX: number
    positionY: number
    positionZ: number
    isActivate: boolean
    dutyUserId?: string
    warehouseId?: string
    areaId?: string
    areaName?: string
    creatorId?: string
    creationTime?: string
    lastModifierId?: string
    lastModificationTime?: string
    extraProperties: Record<string, object>
    concurrencyStamp?: string
}

export interface PurchaseItemsSubmitApprovalDto {
    id: string
}

export interface PurchaseItemsApprovalDto {
    id: string
    isApproved: boolean | null
    note?: string
}

export interface PurchaseItemsProcurementDto {
    id: string
}

export interface PurchaseItemsOrderDto {
    id: string
    isOrder?: boolean
}

export interface PurchaseItemsRefundedDto {
    id: string
}

/**
 * 提交出库DTO
 */
export interface WmOutWarehouseListSubmitDto {
    /**
     * 出库明细Id
     */
    id: string
}

/**
 * 检验DTO
 */
export interface WmOutWarehouseListInProgressDto {
    /**
     * 出库明细Id
     */
    id: string
}

/**
 * 出库DTO
 */
export interface WmOutWarehouseListIsOutDto {
    /**
     * 出库明细Id
     */
    id: string
    /**
     * 是否出库
     */
    isOut: boolean
}

/**
 * 退回DTO
 */
export interface WmOutWarehouseListIsCancelDto {
    /**
     * 出库明细Id
     */
    id: string
    /**
     * 是否取消
     */
    isCancel: boolean
}

export interface PurchaseItemsReturnModifyDto {
    /**
     * 采购明细Id
     */
    id: string
}

/**
 * 采购明细完成DTO
 */
export interface PurchaseItemsCompletedDto {
    /**
     * 采购明细Id
     */
    id: string
}

/**
 * 物料流水日志输入参数
 */
export interface GetWmMaterialFlowLogInput extends PagedSortedAndFilteredInputDto {
    /**
     * 物料ID
     */
    materialId?: string
    /**
     * 是否已删除
     */
    isDeleted?: boolean
    /**
     * 排序参数
     */
    sorting?: string
}

/**
 * 物料流水日志列表DTO
 */
export interface WmMaterialFlowLogListDto extends EntityDto<string> {
    /**
     * 物料ID
     */
    materialId?: string
    /**
     * 物料名称
     */
    materialName?: string
    /**
     * 数量
     */
    quantity: number
    /**
     * 操作类型
     */
    operationType: string
    /**
     * 来源类型
     */
    sourceType: string
    /**
     * 来源ID
     */
    sourceId?: string
    /**
     * 备注
     */
    remark?: string
    /**
     * 创建者ID
     */
    creatorId?: string
    /**
     * 创建时间
     */
    creationTime?: string
    /**
     * 最后修改者ID
     */
    lastModifierId?: string
    /**
     * 最后修改时间
     */
    lastModificationTime?: string
    /**
     * 额外属性
     */
    extraProperties: Record<string, object>
    /**
     * 并发标记
     */
    concurrencyStamp?: string

    /**
     * 申请人ID
     */
    applicantUserId?: string
    /**
     * 申请人名称
     */
    applicantUserName?: string
    /**
     * 审核人ID
     */
    reviewerUserId?: string
    /**
     * 审核人名称
     */
    reviewerUserName?: string
    /**
     * 执行人ID
     */
    executorUserId?: string
    /**
     * 执行人名称
     */
    executorUserName?: string
    /**
     * 创建者名称
     */
    creatorName?: string
    /**
     * 最后修改者名称
     */
    lastModifierName?: string

    /**
     * 类别ID
     */
    categoryId?: string
    /**
     * 类别名称
     */
    categoryName?: string
    /**
     * 计量单位ID
     */
    measureUnitId?: string
    /**
     * 计量单位名称
     */
    measureUnitName?: string
    /**
     * 是否激活
     */
    isActivate?: boolean
    /**
     * 最小库存
     */
    minStock?: number
    /**
     * 最大库存
     */
    maxStock?: number
    /**
     * 描述
     */
    describe?: string
    /**
     * 物料审核状态
     */
    materialsAuditStatus?: string
    /**
     * 物料类型
     */
    type?: string
}

/**
 * 物料流水日志详情DTO
 */
export interface WmMaterialFlowLogDetailDto extends EntityDto<string> {
    /**
     * 物料ID
     */
    materialId?: string
    /**
     * 物料名称
     */
    materialName?: string
    /**
     * 数量
     */
    quantity: number
    /**
     * 操作类型
     */
    operationType: string
    /**
     * 来源类型
     */
    sourceType: string
    /**
     * 来源ID
     */
    sourceId?: string
    /**
     * 备注
     */
    remark?: string
    /**
     * 创建者ID
     */
    creatorId?: string
    /**
     * 创建时间
     */
    creationTime?: string
    /**
     * 最后修改者ID
     */
    lastModifierId?: string
    /**
     * 最后修改时间
     */
    lastModificationTime?: string
    /**
     * 额外属性
     */
    extraProperties: Record<string, object>
    /**
     * 并发标记
     */
    concurrencyStamp?: string
}

export interface PurchaseItemsCreateMaterialByTemporaryDto {
    /**
     * 临时采购项的ID
     */
    id: string
    /**
     * 名称
     */
    name: string

    /**
     * 编码
     */
    encode?: string

    /**
     * 型号
     */
    model: string

    /**
     * 规格
     */
    specification?: string

    /**
     * 订单描述
     */
    description?: string

    /**
     * 计量单位Id
     */
    measureUnitId: string

    /**
     * 类别Id
     */
    categoryId: string

    /**
     * 供应商Id
     */
    supplierId: string

    /**
     * 库位Id
     */
    locationId: string

    /**
     * 设置安全库存
     */
    safeStock?: string

    /**
     * 采购数量
     */
    quantity: number

    /**
     * 实际到达时间 (Actual Time of Arrival)
     */
    ata: string

    /**
     * 单价
     */
    unitPrice: number

    /**
     * 图片路径
     */
    images?: string

    /**
     * 申请人Id
     */
    applicantUserId: string

    /**
     * 申请人名称（申请人无系统账号）
     */
    applicantUserName?: string

    /**
     * 审核人Id
     */
    reviewerUserId: string

    /**
     * 执行人Id
     */
    executorUserId: string

    /**
     * 执行人名称（执行人无系统账号）
     */
    executorUserName?: string
}

export interface Statistics {
    max: number
    min: number
    average: number
    last: number
    totalCount: number
    totalPrice: number
    safeStock?: string | number
    minStock?: number
    maxStock?: number
    stockQuantityChange?: Record<string, any>
}

export interface WmMaterialFlowLogDto extends EntityDto<string> {
    materialId?: string
    type?: string
    outboundType?: string
    quantity?: number
    describe?: string
    applicantUserId?: string
    applicantUserName?: string
    reviewerUserId?: string
    executorUserId?: string
    executorUserName?: string
    creatorId?: string
    creationTime?: string
}
